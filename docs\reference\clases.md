# Referencia de Clases

*Generado automáticamente el 2025-09-11 16:35:25*

Este documento contiene la documentación de todas las clases, interfaces, traits y enums del proyecto.

---

## Namespace: `App\Traits`


### trait: `HasInventory`

**Nombre completo:** `App\Traits\HasInventory`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Traits\HasInventory.php`

---


## Namespace: `LBCDev\Ecommerce`


### class: `EcommerceServiceProvider`

**Nombre completo:** `LBCDev\Ecommerce\EcommerceServiceProvider`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\EcommerceServiceProvider.php`

**Extiende:** `ServiceProvider`

#### Métodos

##### `public function register(): void`

##### `public function boot(): void`

---


## Namespace: `LBCDev\Ecommerce\Console\Commands`


### class: `CleanupExpiredCarts`

**Nombre completo:** `LBCDev\Ecommerce\Console\Commands\CleanupExpiredCarts`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Console\Commands\CleanupExpiredCarts.php`

**Extiende:** `Command`

#### Propiedades

- `protected $signature = 'ecommerce:cleanup-carts'`

- `protected $description = 'Clean up expired carts from database'`

- ` $deletedCount = PersistentCart::expired()->count()`

#### Métodos

##### `public function handle(): int`

---


## Namespace: `LBCDev\Ecommerce\Contracts`


### interface: `CartStorageInterface`

**Nombre completo:** `LBCDev\Ecommerce\Contracts\CartStorageInterface`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Contracts\CartStorageInterface.php`

---


## Namespace: `LBCDev\Ecommerce\Enums`


### enum: `Currency`

**Nombre completo:** `LBCDev\Ecommerce\Enums\Currency`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Enums\Currency.php`

**Extiende:** `string`

---


### enum: `OrderStatus`

**Nombre completo:** `LBCDev\Ecommerce\Enums\OrderStatus`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Enums\OrderStatus.php`

**Extiende:** `string`

---


## Namespace: `LBCDev\Ecommerce\Events`


### class: `OrderCreated`

**Nombre completo:** `LBCDev\Ecommerce\Events\OrderCreated`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Events\OrderCreated.php`

#### Métodos

##### `public function __construct(Order $order)`

---


### class: `OrderStatusChanged`

**Nombre completo:** `LBCDev\Ecommerce\Events\OrderStatusChanged`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Events\OrderStatusChanged.php`

#### Métodos

##### `public function __construct(Order $order, string $previousStatus, string $newStatus)`

---


### class: `UserLoggedIn`

**Nombre completo:** `LBCDev\Ecommerce\Events\UserLoggedIn`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Events\UserLoggedIn.php`

#### Propiedades

- ` $cartService = app(CartService::class)`

#### Métodos

##### `public function handle(Login $event): void`

---


## Namespace: `LBCDev\Ecommerce\Facades`


### class: `Ecommerce`

**Nombre completo:** `LBCDev\Ecommerce\Facades\Ecommerce`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Facades\Ecommerce.php`

**Extiende:** `Facade`

**Descripción:**
```
@method static void registerLinkable(string $modelClass, array $config = [])
@method static array getAllLinkables()
@method static array|null getLinkable(string $modelClass)
@method static bool isLinkableRegistered(string $modelClass)
@method static \Illuminate\Support\Collection getLinkablesForSelect()
@method static array getAllowedPurposes(string $modelClass)
@method static string getDefaultPurpose(string $modelClass)
@method static string getDefaultAccessPolicy(string $modelClass)
```

#### Métodos

##### `protected static function getFacadeAccessor()`

##### `public static function registerLinkable(string $modelClass, array $config = '[]'): void`

```
Registrar un tipo linkable
```

##### `public static function getAllLinkables(): array`

```
Obtener todos los tipos linkables
```

##### `public static function getLinkable(string $modelClass): ?array`

```
Obtener configuración de un tipo específico
```

##### `public static function isLinkableRegistered(string $modelClass): bool`

```
Verificar si un tipo está registrado
```

##### `public static function getLinkablesForSelect(): \Illuminate\Support\Collection`

```
Obtener tipos para select
```

##### `public static function getAllowedPurposes(string $modelClass): array`

```
Obtener propósitos permitidos
```

##### `public static function getDefaultPurpose(string $modelClass): string`

```
Obtener propósito por defecto
```

##### `public static function getDefaultAccessPolicy(string $modelClass): string`

```
Obtener política de acceso por defecto
```

---


## Namespace: `LBCDev\Ecommerce\Helpers`


### class: `TaxonomySlugHelper`

**Nombre completo:** `LBCDev\Ecommerce\Helpers\TaxonomySlugHelper`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Helpers\TaxonomySlugHelper.php`

#### Propiedades

- ` $prefixes = config('lbcdev-ecommerce.taxonomy_slug_prefixes...`

- ` $prefixes = config('lbcdev-ecommerce.taxonomy_slug_prefixes...`

- ` $prefix = $prefixes[$type] ?? ''`

#### Métodos

##### `public static function withPrefix(string $slug, string $type): string`

##### `public static function stripPrefix(string $slug, string $type): string`

---


## Namespace: `LBCDev\Ecommerce\Models`


### class: `Address`

**Nombre completo:** `LBCDev\Ecommerce\Models\Address`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\Address.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_addresses'`

- `protected $fillable = [
        'line1',
        'line2',
        ...`

#### Métodos

##### `public function user(): BelongsTo`

##### `public function getFullAddressAttribute(): string`

##### `protected static function newFactory()`

---


### class: `Cart`

**Nombre completo:** `LBCDev\Ecommerce\Models\Cart`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\Cart.php`

#### Propiedades

- `protected array $items = []`
  ```
  Array asociativo de CartItems, la clave será "type:id" para evitar duplicados.
@var CartItem[]
  ```

- ` $key = $this->getItemKey($sellable)`
  ```
  Añade un ítem vendible al carrito.
Si el ítem ya existe, incrementa la cantidad.
@param mixed $sellable Modelo vendible (debe usar Trait Sellable)
@param int $quantity
@return void
  ```

- ` $existing = $this->items[$key]`

- ` $key = $this->getItemKey($sellable)`
  ```
  Remueve un ítem del carrito.
@param mixed $sellable
@return void
  ```

- ` $key = $this->getItemKey($sellable)`
  ```
  Cambia la cantidad de un ítem del carrito.
Si la cantidad es menor que 1, se remueve el ítem.
@param mixed $sellable
@param int $quantity
@return void
  ```

- ` return $item`
  ```
  Busca un ítem en el carrito.
@param mixed $sellable
@return CartItem|null
  ```

- ` $total = 0`
  ```
  Calcula el total sin impuestos.
@return float
  ```

- ` return $total`

- ` $total = 0`
  ```
  Calcula el total con impuestos.
@return float
  ```

- ` return $total`

- ` $type = $sellable->getSellableType()`
  ```
  Genera una clave única para identificar un ítem (type + id).
@param mixed $sellable
@return string
  ```

- ` $id = $sellable->getSellableId()`
  ```
  Genera una clave única para identificar un ítem (type + id).
@param mixed $sellable
@return string
  ```

#### Métodos

##### `public function addItem($sellable, int $quantity = '1'): void`

```
Añade un ítem vendible al carrito.
Si el ítem ya existe, incrementa la cantidad.
@param mixed $sellable Modelo vendible (debe usar Trait Sellable)
@param int $quantity
@return void
```

##### `public function removeItem($sellable): void`

```
Remueve un ítem del carrito.
@param mixed $sellable
@return void
```

##### `public function updateQuantity($sellable, int $quantity): void`

```
Cambia la cantidad de un ítem del carrito.
Si la cantidad es menor que 1, se remueve el ítem.
@param mixed $sellable
@param int $quantity
@return void
```

##### `public function findItem($sellable): ?CartItem`

```
Busca un ítem en el carrito.
@param mixed $sellable
@return CartItem|null
```

##### `public function getItems(): array`

```
Obtiene todos los ítems del carrito.
@return CartItem[]
```

##### `public function getTotal(): float`

```
Calcula el total sin impuestos.
@return float
```

##### `public function getTotalWithTax(): float`

```
Calcula el total con impuestos.
@return float
```

##### `public function clear(): void`

```
Vacía el carrito.
@return void
```

##### `protected function getItemKey($sellable): string`

```
Genera una clave única para identificar un ítem (type + id).
@param mixed $sellable
@return string
```

---


### class: `CartItem`

**Nombre completo:** `LBCDev\Ecommerce\Models\CartItem`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\CartItem.php`

#### Propiedades

- `protected $item`
  ```
  El modelo vendible (que usa el Trait Sellable).
@var mixed
  ```

- `protected int $quantity`
  ```
  Cantidad de unidades de este ítem en el carrito.
@var int
  ```

#### Métodos

##### `public function __construct($item, int $quantity = '1')`

```
Constructor.
@param mixed $item   Modelo vendible (debe implementar métodos Sellable)
@param int $quantity
```

##### `public function getItem()`

```
Obtener el modelo vendible.
@return mixed
```

##### `public function getQuantity(): int`

```
Obtener la cantidad.
@return int
```

##### `public function setQuantity(int $quantity): void`

```
Modificar la cantidad.
@param int $quantity
@return void
```

##### `public function getUnitPrice(): float`

```
Obtener el precio unitario sin impuestos.
@return float
```

##### `public function getUnitPriceWithTax(): float`

```
Obtener el precio unitario con impuestos.
@return float
```

##### `public function getSubtotal(): float`

```
Obtener el subtotal sin impuestos (precio unitario * cantidad).
@return float
```

##### `public function getSubtotalWithTax(): float`

```
Obtener el subtotal con impuestos (precio unitario con impuestos * cantidad).
@return float
```

##### `public function getName(): string`

```
Obtener el nombre del ítem.
@return string
```

##### `public function getId()`

```
Obtener el identificador del ítem.
@return mixed
```

##### `public function getType(): string`

```
Obtener el tipo/clase del ítem.
@return string
```

##### `public function matches($sellable): bool`

```
Verificar si este ítem es igual a otro.
@param mixed $sellable
@return bool
```

---


### class: `Category`

**Nombre completo:** `LBCDev\Ecommerce\Models\Category`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\Category.php`

**Extiende:** `Taxonomy`

#### Propiedades

- `protected $table = 'taxonomies'`

- `protected $guarded = []`

- ` $type = config('lbcdev-ecommerce.categories_taxonomy_ty...`

#### Métodos

##### `public function __construct(array $attributes = '[]')`

##### `protected static function booted(): void`

##### `public function getRouteKeyName(): string`

##### `public function parent(): BelongsTo`

##### `public function children(): HasMany`

##### `public static function root(): self`

##### `protected static function newFactory()`

---


### class: `Order`

**Nombre completo:** `LBCDev\Ecommerce\Models\Order`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\Order.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_orders'`

- `protected $fillable = [
        'user_id',
        'order_number',
...`

- `protected $casts = [
        'subtotal' => 'float',
        'tax...`

- ` $order = static::create([
            'user_id' => $use...`

- ` return $order`

- ` $validStatuses = OrderStatus::getPossibleValues()`
  ```
  Cambiar estado de la orden.
  ```

- ` $prefix = config('ecommerce.order_number_prefix', 'ORD')`
  ```
  Generar número de orden único.
  ```

- ` $timestamp = now()->format('Ymd')`
  ```
  Generar número de orden único.
  ```

- ` $lastOrder = static::whereDate('created_at', today())
     ...`

- ` $sequence = $lastOrder ? (int) substr($lastOrder->order_num...`

#### Métodos

##### `protected static function boot()`

```
Generar número de orden único.
```

##### `public function items(): HasMany`

```
Relación con los items de la orden.
```

##### `public function user(): BelongsTo`

```
Relación con el usuario (opcional).
```

##### `public static function createFromCart(Cart $cart, array $customerData = '[]', int $userId = 'null'): self`

```
Crear orden desde carrito.
```

##### `public function updateStatus(string $status): void`

```
Cambiar estado de la orden.
```

##### `public function canBeCancelled(): bool`

```
Verificar si la orden puede ser cancelada.
```

##### `public function canBeRefunded(): bool`

```
Verificar si la orden puede ser reembolsada.
```

##### `public static function getAvailableStatuses(): array`

```
Obtener estados disponibles.
```

##### `protected static function generateOrderNumber(): string`

```
Generar número de orden único.
```

##### `public function scopeWithStatus($query, string $status)`

```
Scope para filtrar por estado.
```

##### `public function scopeRecent($query, int $days = '30')`

```
Scope para órdenes recientes.
```

##### `protected static function newFactory()`

```
Scope para órdenes recientes.
```

---


### class: `OrderItem`

**Nombre completo:** `LBCDev\Ecommerce\Models\OrderItem`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\OrderItem.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_order_items'`

- `protected $fillable = [
        'order_id',
        'sellable_type'...`

- `protected $casts = [
        'quantity' => 'integer',
        'u...`

#### Métodos

##### `public function order(): BelongsTo`

```
Relación con la orden.
```

##### `public function sellable(): MorphTo`

```
Relación polimórfica con el item vendible.
```

##### `public function hasOriginalItem(): bool`

```
Verificar si el item original aún existe.
```

##### `public function getUnitPriceWithTax(): float`

```
Obtener el precio unitario con impuestos.
```

##### `protected static function newFactory()`

```
Obtener el precio unitario con impuestos.
```

---


### class: `PersistentCart`

**Nombre completo:** `LBCDev\Ecommerce\Models\PersistentCart`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\PersistentCart.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_carts'`

- `protected $fillable = [
        'user_id',
        'session_id',
 ...`

- `protected $casts = [
        'total' => 'float',
        'total_...`

- ` $cart = new Cart()`
  ```
  Convertir a Cart en memoria.
  ```

- ` return $cart`

#### Métodos

##### `public function items(): HasMany`

```
Relación con los items del carrito.
```

##### `public function user(): BelongsTo`

```
Relación con el usuario.
```

##### `public function toMemoryCart(): Cart`

```
Convertir a Cart en memoria.
```

##### `public function updateFromMemoryCart(Cart $memoryCart): void`

```
Actualizar desde Cart en memoria.
```

##### `public function scopeExpired($query)`

```
Scope para carritos expirados.
```

##### `public function scopeActive($query)`

```
Scope para carritos activos.
```

---


### class: `PersistentCartItem`

**Nombre completo:** `LBCDev\Ecommerce\Models\PersistentCartItem`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\PersistentCartItem.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_cart_items'`

- `protected $fillable = [
        'cart_id',
        'sellable_type',...`

- `protected $casts = [
        'quantity' => 'integer',
        'u...`

#### Métodos

##### `public function cart(): BelongsTo`

```
Relación con el carrito persistente.
```

##### `public function sellable(): MorphTo`

```
Relación polimórfica con el item vendible.
```

##### `public function hasOriginalItem(): bool`

```
Verificar si el item original aún existe.
```

##### `public function getSubtotal(): float`

```
Obtener subtotal sin impuestos.
```

##### `public function getSubtotalWithTax(): float`

```
Obtener subtotal con impuestos.
```

---


### class: `Product`

**Nombre completo:** `LBCDev\Ecommerce\Models\Product`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\Product.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_products'`

- `protected $fillable = [
        'name',
        'description',
   ...`

- `protected $casts = [
        'price' => 'decimal:2',
        'ta...`

- ` $defaultAttributes = [
            'purpose' => 'included',
      ...`
  ```
  Vincular un modelo al producto
  ```

- ` $attributes = array_merge($defaultAttributes, $attributes)`

#### Métodos

##### `public function variants()`

##### `public function linkables()`

```
Obtener todos los linkables asociados al producto
```

##### `public function linkablesOfType(string $type)`

```
Obtener linkables de un tipo específico
```

##### `public function linkModel($model, array $attributes = '[]')`

```
Vincular un modelo al producto
```

##### `public function getCategoriesAttribute()`

##### `public function getTagsAttribute()`

##### `public function getPriceWithDiscountAttribute()`

##### `protected static function newFactory()`

---


### class: `ProductLinkable`

**Nombre completo:** `LBCDev\Ecommerce\Models\ProductLinkable`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\ProductLinkable.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_productables'`

- `protected $fillable = [
        'product_id',
        'productable_ty...`

- `protected $casts = [
        'meta' => 'array',
    ]`

#### Métodos

##### `public function product(): BelongsTo`

```
Relación con el producto
```

##### `public function productable(): MorphTo`

```
Relación polimórfica con el modelo linkable
```

---


### class: `ProductVariant`

**Nombre completo:** `LBCDev\Ecommerce\Models\ProductVariant`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\ProductVariant.php`

**Extiende:** `Model`

#### Propiedades

- `protected $table = 'lbcdev_ecommerce_product_variants'`

- `protected $fillable = [
        'product_id',
        'sku',
     ...`

- `protected $casts = [
        'price' => 'float',
        'tax_ra...`

- ` $exists = static::where('sku', $variant->sku)
          ...`

- ` $baseName = $this->product?->name ?? 'Unnamed'`

- ` $attrStr = collect($this->attributes)
            ->map(f...`

#### Métodos

##### `public static function booted(): void`

##### `public function product(): BelongsTo`

##### `public function getSellableName(): string`

##### `public function getSellablePrice(): float`

##### `public function getSellableTaxRate(): float`

##### `protected static function newFactory()`

---


### class: `Tag`

**Nombre completo:** `LBCDev\Ecommerce\Models\Tag`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Models\Tag.php`

**Extiende:** `Taxonomy`

#### Propiedades

- `protected $table = 'taxonomies'`

#### Métodos

##### `public function __construct(array $attributes = '[]')`

##### `protected static function booted(): void`

##### `public function getRouteKeyName(): string`

##### `protected static function newFactory()`

---


## Namespace: `LBCDev\Ecommerce\Providers`


### class: `CartServiceProvider`

**Nombre completo:** `LBCDev\Ecommerce\Providers\CartServiceProvider`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Providers\CartServiceProvider.php`

**Extiende:** `ServiceProvider`

#### Propiedades

- ` $driver = config('ecommerce.cart.driver', 'session')`

#### Métodos

##### `public function register(): void`

##### `public function boot(): void`

---


## Namespace: `LBCDev\Ecommerce\Services`


### class: `CartService`

**Nombre completo:** `LBCDev\Ecommerce\Services\CartService`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Services\CartService.php`

#### Propiedades

- `protected CartStorageInterface $storage`

- `protected Session $session`

- `protected AuthManager $auth`

- ` $cart = $this->getCart()`

- ` $existingItem = $cart->findItem($sellable)`

- ` return $cart`

- ` $cart = $this->getCart()`

- ` return $cart`

- ` $cart = $this->getCart()`

- ` return $cart`

- ` $sessionIdentifier = $this->getSessionIdentifier()`

- ` $userIdentifier = $this->getUserIdentifier($user)`

- ` $user = $this->getAuthenticatedUser()`

- ` $user = $this->auth->guard($guard)->user()`

- ` return $user`

#### Métodos

##### `public function __construct(CartStorageInterface $storage, Session $session, AuthManager $auth)`

##### `public function getCart(): Cart`

##### `public function saveCart(Cart $cart): void`

##### `public function clearCart(): void`

##### `public function addItem($sellable, int $quantity = '1'): Cart`

##### `public function removeItem($sellable): Cart`

##### `public function updateQuantity($sellable, int $quantity): Cart`

##### `public function handleUserLogin($user): Cart`

##### `protected function getCartIdentifier(): string`

##### `protected function getUserIdentifier($user): string`

##### `protected function getSessionIdentifier(): string`

##### `protected function getAuthenticatedUser(): ?\Illuminate\Contracts\Auth\Authenticatable`

---


### class: `LinkableRegistry`

**Nombre completo:** `LBCDev\Ecommerce\Services\LinkableRegistry`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Services\LinkableRegistry.php`

#### Propiedades

- `protected static array $linkables = []`

- ` $defaultConfig = [
            'label' => class_basename($modelC...`
  ```
  Registrar un tipo linkable
  ```

- ` $config = static::get($modelClass)`
  ```
  Obtener propósitos permitidos para un tipo
  ```

- ` $config = static::get($modelClass)`
  ```
  Obtener propósito por defecto para un tipo
  ```

- ` $config = static::get($modelClass)`
  ```
  Obtener política de acceso por defecto para un tipo
  ```

- ` $configLinkables = config('lbcdev-ecommerce.linkables', [])`
  ```
  Cargar tipos desde configuración
  ```

#### Métodos

##### `public static function register(string $modelClass, array $config = '[]'): void`

```
Registrar un tipo linkable
```

##### `public static function all(): array`

```
Obtener todos los tipos linkables registrados
```

##### `public static function get(string $modelClass): ?array`

```
Obtener configuración de un tipo específico
```

##### `public static function isRegistered(string $modelClass): bool`

```
Verificar si un tipo está registrado
```

##### `public static function getForSelect(): Collection`

```
Obtener tipos como Collection para uso en formularios
```

##### `public static function getAllowedPurposes(string $modelClass): array`

```
Obtener propósitos permitidos para un tipo
```

##### `public static function getDefaultPurpose(string $modelClass): string`

```
Obtener propósito por defecto para un tipo
```

##### `public static function getDefaultAccessPolicy(string $modelClass): string`

```
Obtener política de acceso por defecto para un tipo
```

##### `public static function clear(): void`

```
Limpiar el registry (útil para testing)
```

##### `public static function loadFromConfig(): void`

```
Cargar tipos desde configuración
```

---


### class: `OrderService`

**Nombre completo:** `LBCDev\Ecommerce\Services\OrderService`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Services\OrderService.php`

#### Propiedades

- ` array $customerData = [],
        ?int $userId = null
    ): Order ...`
  ```
  Crear una orden desde un carrito.
  ```

- ` $order = Order::createFromCart($cart, $customerData, $us...`

- ` return $order`

- ` $previousStatus = $order->status`
  ```
  Actualizar el estado de una orden.
  ```

- ` $required = ['name', 'email']`
  ```
  Validar datos del cliente.
  ```

#### Métodos

##### `public function createOrderFromCart(Cart $cart, array $customerData = '[]', int $userId = 'null'): Order`

```
Crear una orden desde un carrito.
```

##### `public function updateOrderStatus(Order $order, string $newStatus): Order`

```
Actualizar el estado de una orden.
```

##### `public function cancelOrder(Order $order, string $reason = '\'\''): Order`

```
Cancelar una orden.
```

##### `public function processOrder(Order $order): Order`

```
Marcar orden como procesando.
```

##### `public function shipOrder(Order $order, array $trackingInfo = '[]'): Order`

```
Marcar orden como enviada.
```

##### `public function deliverOrder(Order $order): Order`

```
Marcar orden como entregada.
```

##### `protected function validateCustomerData(array $customerData): void`

```
Validar datos del cliente.
```

---


## Namespace: `LBCDev\Ecommerce\Services\CartStorage`


### class: `DatabaseCartStorage`

**Nombre completo:** `LBCDev\Ecommerce\Services\CartStorage\DatabaseCartStorage`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Services\CartStorage\DatabaseCartStorage.php`

**Implementa:** `CartStorageInterface`

#### Propiedades

- ` $persistentCart = $this->findPersistentCart($identifier)`

- ` $persistentCart = $this->findOrCreatePersistentCart($identifier)`

- ` $persistentCart = $this->findPersistentCart($identifier)`

- ` $fromCart = $this->getCart($fromIdentifier)`

- ` $toCart = $this->getCart($toIdentifier)`

- ` return $toCart`

#### Métodos

##### `public function getCart(string $identifier): Cart`

##### `public function saveCart(string $identifier, Cart $cart): void`

##### `public function clearCart(string $identifier): void`

##### `public function hasCart(string $identifier): bool`

##### `public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart`

##### `protected function findPersistentCart(string $identifier): ?PersistentCart`

##### `protected function findOrCreatePersistentCart(string $identifier): PersistentCart`

---


### class: `HybridCartStorage`

**Nombre completo:** `LBCDev\Ecommerce\Services\CartStorage\HybridCartStorage`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Services\CartStorage\HybridCartStorage.php`

**Implementa:** `CartStorageInterface`

#### Propiedades

- `protected SessionCartStorage $sessionStorage`

- `protected DatabaseCartStorage $databaseStorage`

- ` $sessionCart = $this->sessionStorage->getCart($fromIdentifier)`

- ` $databaseCart = $this->databaseStorage->getCart($toIdentifier)`

- ` return $databaseCart`

#### Métodos

##### `public function __construct(SessionCartStorage $sessionStorage, DatabaseCartStorage $databaseStorage)`

##### `public function getCart(string $identifier): Cart`

##### `public function saveCart(string $identifier, Cart $cart): void`

##### `public function clearCart(string $identifier): void`

##### `public function hasCart(string $identifier): bool`

##### `public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart`

##### `public function migrateSessionToUser(string $sessionIdentifier, string $userIdentifier): Cart`

```
Migrar carrito de sesión a persistente al hacer login.
```

##### `protected function shouldUsePersistentStorage(string $identifier): bool`

```
Migrar carrito de sesión a persistente al hacer login.
```

---


### class: `SessionCartStorage`

**Nombre completo:** `LBCDev\Ecommerce\Services\CartStorage\SessionCartStorage`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Services\CartStorage\SessionCartStorage.php`

**Implementa:** `CartStorageInterface`

#### Propiedades

- `protected string $sessionKey`

- ` $cartData = Session::get($this->getSessionKey($identifier))`

- ` $cartData = $this->serializeCart($cart)`

- ` $fromCart = $this->getCart($fromIdentifier)`

- ` $toCart = $this->getCart($toIdentifier)`

- ` return $toCart`

- ` $items = []`

- ` $cart = new Cart()`

- ` $sellableClass = $itemData['sellable_type']`

- ` $sellable = $sellableClass::find($itemData['sellable_id'])`

- ` return $cart`

#### Métodos

##### `public function __construct()`

##### `public function getCart(string $identifier): Cart`

##### `public function saveCart(string $identifier, Cart $cart): void`

##### `public function clearCart(string $identifier): void`

##### `public function hasCart(string $identifier): bool`

##### `public function mergeCarts(string $fromIdentifier, string $toIdentifier): Cart`

##### `protected function getSessionKey(string $identifier): string`

##### `protected function serializeCart(Cart $cart): array`

##### `protected function deserializeCart(array $cartData): Cart`

---


## Namespace: `LBCDev\Ecommerce\Traits`


### trait: `HasCategories`

**Nombre completo:** `LBCDev\Ecommerce\Traits\HasCategories`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Traits\HasCategories.php`

---


### trait: `HasTags`

**Nombre completo:** `LBCDev\Ecommerce\Traits\HasTags`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Traits\HasTags.php`

---


### trait: `Sellable`

**Nombre completo:** `LBCDev\Ecommerce\Traits\Sellable`

**Archivo:** `.\vendor\lbcdev\ecommerce\src\Traits\Sellable.php`

**Descripción:**
```
Trait Sellable
Allows a model to be treated as a sellable item in the e-commerce system.
@property string|null $title
@property string|null $name
@property float|null $price
@property float|null $tax_rate
```

---

